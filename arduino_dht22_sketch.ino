/*
 * Arduino DHT22 Temperature Sensor Sketch
 * 
 * This sketch reads temperature from a DHT22 sensor and sends it via serial
 * when requested by the Python Flask application.
 * 
 * Connections:
 * DHT22 VCC -> Arduino 5V (or 3.3V)
 * DHT22 GND -> Arduino GND
 * DHT22 DATA -> Arduino Digital Pin 2
 * 
 * Required Library: DHT sensor library by Adafruit
 * Install via Arduino IDE: Tools -> Manage Libraries -> Search "DHT sensor library"
 */

#include "DHT.h"

#define DHT_PIN 2        // Digital pin connected to the DHT sensor
#define DHT_TYPE DHT22   // DHT 22 (AM2302)

DHT dht(DHT_PIN, DHT_TYPE);

void setup() {
  Serial.begin(9600);
  Serial.println("DHT22 Temperature Sensor Ready");
  dht.begin();
  delay(2000); // Wait for sensor to stabilize
}

void loop() {
  // Check if there's a command from the Python script
  if (Serial.available() > 0) {
    String command = Serial.readStringUntil('\n');
    command.trim();
    
    if (command == "READ_TEMP") {
      // Read temperature from DHT22
      float temperature = dht.readTemperature();
      
      // Check if reading was successful
      if (isnan(temperature)) {
        Serial.println("ERROR: Failed to read from DHT sensor!");
      } else {
        // Send temperature back to Python
        Serial.print("TEMP:");
        Serial.println(temperature, 1); // 1 decimal place
      }
    }
  }
  
  delay(100); // Small delay to prevent overwhelming the serial port
}
