# Arduino DHT22 Setup Instructions

## Hardware Setup

### Required Components:
- Arduino Uno (or compatible)
- DHT22 temperature and humidity sensor
- Breadboard and jumper wires
- USB cable to connect Arduino to computer

### Wiring:
```
DHT22 Pin 1 (VCC) -> Arduino 5V
DHT22 Pin 2 (DATA) -> Arduino Digital Pin 2
DHT22 Pin 3 (NC) -> Not connected
DHT22 Pin 4 (GND) -> Arduino GND
```

Note: You may need a 10kΩ pull-up resistor between VCC and DATA pins for reliable operation.

## Software Setup

### 1. Install Arduino IDE
Download and install the Arduino IDE from https://www.arduino.cc/en/software

### 2. Install DHT Library
1. Open Arduino IDE
2. Go to Tools -> Manage Libraries
3. Search for "DHT sensor library" by Adafruit
4. Install the "DHT sensor library" and its dependencies

### 3. Upload the Sketch
1. Open the `arduino_dht22_sketch.ino` file in Arduino IDE
2. Connect your Arduino to the computer via USB
3. Select the correct board: Tools -> Board -> Arduino Uno
4. Select the correct port: Tools -> Port -> (select your Arduino port)
5. Click Upload button

### 4. Find the Arduino Port
After uploading, you need to identify the correct serial port:

**On Linux:**
- Usually `/dev/ttyUSB0` or `/dev/ttyACM0`
- Check with: `ls /dev/tty*`

**On Windows:**
- Usually `COM3`, `COM4`, etc.
- Check in Device Manager

**On macOS:**
- Usually `/dev/cu.usbmodem*` or `/dev/cu.usbserial*`
- Check with: `ls /dev/cu.*`

### 5. Update Python Configuration
Edit the `ARDUINO_PORT` variable in `venv/app.py` to match your Arduino's port:

```python
ARDUINO_PORT = '/dev/ttyACM0'  # Change this to your Arduino's port
```

## Testing

### 1. Test Arduino Directly
1. Open Arduino IDE Serial Monitor (Tools -> Serial Monitor)
2. Set baud rate to 9600
3. Type `READ_TEMP` and press Enter
4. You should see a response like `TEMP:23.4`

### 2. Test with Python App
1. Make sure Arduino is connected and the sketch is running
2. Start the Flask app: `python3 venv/app.py`
3. Open the web interface
4. Click "Read DHT22 Sensor" button
5. The temperature should be read from the actual sensor

## Troubleshooting

### Common Issues:

1. **Permission denied on Linux:**
   ```bash
   sudo chmod 666 /dev/ttyUSB0
   # or add your user to the dialout group:
   sudo usermod -a -G dialout $USER
   # then logout and login again
   ```

2. **Sensor reading NaN:**
   - Check wiring connections
   - Ensure DHT22 library is installed
   - Try adding a 10kΩ pull-up resistor

3. **Serial port not found:**
   - Check if Arduino is properly connected
   - Verify the correct port in Device Manager/Terminal
   - Update the `ARDUINO_PORT` variable in app.py

4. **Timeout errors:**
   - Increase `ARDUINO_TIMEOUT` in app.py
   - Check if Arduino sketch is running properly

### Fallback Mode:
If the DHT22 sensor is not available, the app will automatically fall back to generating random temperatures, so the web interface will continue to work.
