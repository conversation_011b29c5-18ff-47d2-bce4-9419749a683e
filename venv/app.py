# get reading from dht22
from flask import Flask, render_template, jsonify, request
import random
import sqlite3

app = Flask(__name__)

# Database configuration
DATABASE = 'temperature.db'

def init_db():
    """Initialize the database with temperature readings table"""
    conn = sqlite3.connect(DATABASE)
    cursor = conn.cursor()
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS temperature_readings (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            temperature REAL NOT NULL,
            timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
        )
    ''')

    # Insert default temperature if table is empty
    cursor.execute('SELECT COUNT(*) FROM temperature_readings')
    if cursor.fetchone()[0] == 0:
        cursor.execute('INSERT INTO temperature_readings (temperature) VALUES (?)', (22.0,))

    conn.commit()
    conn.close()

def get_latest_temperature():
    """Get the latest temperature reading from database"""
    conn = sqlite3.connect(DATABASE)
    cursor = conn.cursor()
    cursor.execute('SELECT temperature FROM temperature_readings ORDER BY id DESC LIMIT 1')
    result = cursor.fetchone()
    conn.close()
    return result[0] if result else 22.0

def save_temperature(temp):
    """Save a new temperature reading to database"""
    conn = sqlite3.connect(DATABASE)
    cursor = conn.cursor()
    cursor.execute('INSERT INTO temperature_readings (temperature) VALUES (?)', (temp,))
    conn.commit()
    conn.close()

def get_pencil_color(temperature):
    """Return color based on temperature value"""
    if 20 <= temperature <= 24:
        return "#00FFFF"  # Cyan for 20-24°C
    elif 25 <= temperature <= 28:
        return "#FFFF00"  # Yellow for 25-28°C
    elif 29 <= temperature <= 33:
        return "#FF0000"  # Red for 29-33°C
    else:
        return "#808080"  # Gray for temperatures outside the specified ranges

@app.route('/')
def index():
    temp = get_latest_temperature()
    return render_template('index.html', temp=temp, color=get_pencil_color(temp))

@app.route('/generate_random')
def generate_random():
    temp = round(random.uniform(18, 35), 1)  # Random temp between 18 and 35 to cover the ranges
    save_temperature(temp)
    return jsonify({'temp': temp, 'message': 'Temperature generated successfully'})

@app.route('/read_temp')
def read_temp():
    temp = get_latest_temperature()
    return jsonify({'temp': temp, 'color': get_pencil_color(temp)})

if __name__ == '__main__':
    init_db()  # Initialize database on startup
    app.run(debug=True, host='0.0.0.0', port=5001)

#python3 venv/app.py