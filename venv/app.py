# get reading from dht22
from flask import Flask, render_template, jsonify, request
import random
import sqlite3
import serial
import time
import json

app = Flask(__name__)

# Database configuration
DATABASE = 'temperature.db'

# Arduino configuration
ARDUINO_PORT = '/dev/ttyACM0'  # Common USB port for Arduino on Linux
ARDUINO_BAUDRATE = 9600
ARDUINO_TIMEOUT = 2

def init_db():
    """Initialize the database with temperature readings table"""
    conn = sqlite3.connect(DATABASE)
    cursor = conn.cursor()
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS temperature_readings (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            temperature REAL NOT NULL,
            timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
        )
    ''')

    # Insert default temperature if table is empty
    cursor.execute('SELECT COUNT(*) FROM temperature_readings')
    if cursor.fetchone()[0] == 0:
        cursor.execute('INSERT INTO temperature_readings (temperature) VALUES (?)', (22.0,))

    conn.commit()
    conn.close()

def get_latest_temperature():
    """Get the latest temperature reading from database"""
    conn = sqlite3.connect(DATABASE)
    cursor = conn.cursor()
    cursor.execute('SELECT temperature FROM temperature_readings ORDER BY id DESC LIMIT 1')
    result = cursor.fetchone()
    conn.close()
    return result[0] if result else 22.0

def save_temperature(temp):
    """Save a new temperature reading to database"""
    conn = sqlite3.connect(DATABASE)
    cursor = conn.cursor()
    cursor.execute('INSERT INTO temperature_readings (temperature) VALUES (?)', (temp,))
    conn.commit()
    conn.close()

def find_arduino_port():
    """Try to automatically find Arduino port"""
    import serial.tools.list_ports

    # Common Arduino port patterns
    arduino_patterns = [
        'ttyUSB',    # Linux USB
        'ttyACM',    # Linux ACM
        'cu.usbmodem',  # macOS
        'cu.usbserial', # macOS
        'COM'        # Windows
    ]

    ports = serial.tools.list_ports.comports()
    for port in ports:
        for pattern in arduino_patterns:
            if pattern in port.device:
                print(f"Found potential Arduino port: {port.device}")
                return port.device

    return None

def read_dht22_temperature():
    """Read temperature from DHT22 sensor connected to Arduino"""
    try:
        # Try configured port first, then auto-detect
        port = ARDUINO_PORT
        if not port or port == '/dev/ttyACM0':  # Default value
            detected_port = find_arduino_port()
            if detected_port:
                port = detected_port
                print(f"Using auto-detected port: {port}")
            else:
                print("No Arduino port detected")
                return None

        # Try to connect to Arduino
        arduino = serial.Serial(port, ARDUINO_BAUDRATE, timeout=ARDUINO_TIMEOUT)
        time.sleep(2)  # Wait for Arduino to initialize

        # Send request for temperature reading
        arduino.write(b'READ_TEMP\n')
        time.sleep(1)  # Wait for sensor reading

        # Read response from Arduino
        response = arduino.readline().decode('utf-8').strip()
        arduino.close()

        # Parse temperature from response
        if response.startswith('TEMP:'):
            temp_str = response.replace('TEMP:', '').strip()
            temperature = float(temp_str)
            return temperature
        else:
            print(f"Unexpected response from Arduino: {response}")
            return None

    except serial.SerialException as e:
        print(f"Serial communication error: {e}")
        return None
    except ValueError as e:
        print(f"Error parsing temperature: {e}")
        return None
    except Exception as e:
        print(f"Unexpected error reading DHT22: {e}")
        return None

def get_pencil_color(temperature):
    """Return color based on temperature value"""
    if 20 <= temperature <= 24:
        return "#00FFFF"  # Cyan for 20-24°C
    elif 25 <= temperature <= 28:
        return "#FFFF00"  # Yellow for 25-28°C
    elif 29 <= temperature <= 33:
        return "#FF0000"  # Red for 29-33°C
    else:
        return "#808080"  # Gray for temperatures outside the specified ranges

@app.route('/')
def index():
    temp = get_latest_temperature()
    return render_template('index.html', temp=temp, color=get_pencil_color(temp))

@app.route('/generate_random')
def generate_random():
    # Read temperature from DHT22 sensor
    temp = read_dht22_temperature()

    if temp is not None:
        # Round to 1 decimal place
        temp = round(temp, 1)
        save_temperature(temp)
        return jsonify({'temp': temp, 'message': 'Temperature read from DHT22 successfully'})
    else:
        # Fallback to random temperature if sensor reading fails
        temp = round(random.uniform(18, 35), 1)
        save_temperature(temp)
        return jsonify({'temp': temp, 'message': 'DHT22 sensor unavailable, generated random temperature'})

@app.route('/read_temp')
def read_temp():
    temp = get_latest_temperature()
    return jsonify({'temp': temp, 'color': get_pencil_color(temp)})

if __name__ == '__main__':
    init_db()  # Initialize database on startup
    app.run(debug=True, host='0.0.0.0', port=5001)

#python3 venv/app.py